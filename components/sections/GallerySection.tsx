import Image from "next/image";
import Link from "next/link";
import { SearchIcon } from "lucide-react";

interface GalleryItem {
  id: number;
  src: string;
  alt: string;
  category: string;
}

const galleryItems: GalleryItem[] = [
  {
    id: 1,
    src: "https://images.pexels.com/photos/5088188/pexels-photo-5088188.jpeg",
    alt: "Intricate bridal mehendi design",
    category: "Bridal",
  },
  {
    id: 2,
    src: "https://images.pexels.com/photos/7139560/pexels-photo-7139560.jpeg",
    alt: "Arabic style mehendi pattern",
    category: "Arabic",
  },
  {
    id: 3,
    src: "https://images.pexels.com/photos/7280300/pexels-photo-7280300.jpeg",
    alt: "Modern minimalist mehendi design",
    category: "Contemporary",
  },
  {
    id: 4,
    src: "https://images.pexels.com/photos/4928017/pexels-photo-4928017.jpeg",
    alt: "Traditional full hand mehendi",
    category: "Traditional",
  },
  {
    id: 5,
    src: "https://images.pexels.com/photos/10626383/pexels-photo-10626383.jpeg",
    alt: "Delicate finger mehendi design",
    category: "Minimal",
  },
  {
    id: 6,
    src: "https://images.pexels.com/photos/10626375/pexels-photo-10626375.jpeg",
    alt: "Bold geometric mehendi pattern",
    category: "Geometric",
  },
];

export default function GallerySection() {
  return (
    <section id="gallery" className="section-padding bg-white">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="section-title centered mx-auto">Portfolio Gallery</h2>
          <p className="max-w-2xl mx-auto text-muted-foreground">
            Browse through a selection of my finest work, showcasing various
            styles from traditional bridal designs to contemporary patterns.
          </p>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {galleryItems.map((item, index) => (
            <div
              key={item.id}
              className={`gallery-item rounded-sm overflow-hidden animate-on-scroll`}
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="relative aspect-[4/5]">
                <Image
                  src={item.src}
                  alt={item.alt}
                  fill
                  className="object-cover"
                />
                <div className="gallery-overlay">
                  <div className="p-4 text-center">
                    <span className="inline-block px-3 py-1 bg-gold/80 text-white text-xs uppercase tracking-wider mb-2">
                      {item.category}
                    </span>
                    <div className="w-10 h-10 bg-white/90 rounded-full flex items-center justify-center mx-auto mt-3">
                      <SearchIcon className="h-5 w-5 text-primary" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="text-center mt-10">
          <Link
            href="https://www.instagram.com/"
            target="_blank"
            rel="noopener noreferrer"
            className="btn btn-outline"
          >
            View More on Instagram
          </Link>
        </div>
      </div>
    </section>
  );
}
