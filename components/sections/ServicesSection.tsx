// "use client";

// import { useState } from 'react';
// import Image from 'next/image';
// import { Check } from 'lucide-react';

// interface Service {
//   id: number;
//   title: string;
//   description: string;
//   price: string;
//   features: string[];
//   popular?: boolean;
//   image: string;
// }

// const services: Service[] = [
//   {
//     id: 1,
//     title: "Bridal Mehendi",
//     description: "Complete bridal mehendi package for hands and feet with intricate traditional designs.",
//     price: "₹8,000 onwards",
//     features: [
//       "Full hands up to elbows",
//       "Full feet up to ankles",
//       "Personalized design consultation",
//       "Complimentary touch-up",
//       "Wedding motifs and symbols"
//     ],
//     popular: true,
//     image: "https://images.pexels.com/photos/5088188/pexels-photo-5088188.jpeg"
//   },
//   {
//     id: 2,
//     title: "Arabic Style",
//     description: "Elegant Arabic-inspired designs featuring floral patterns and negative space.",
//     price: "₹3,500 onwards",
//     features: [
//       "Both hands up to wrist",
//       "Bold, spaced-out patterns",
//       "Floral and vine designs",
//       "Quick-drying formula",
//       "2-3 hour application"
//     ],
//     image: "https://images.pexels.com/photos/7139560/pexels-photo-7139560.jpeg"
//   },
//   {
//     id: 3,
//     title: "Party Mehendi",
//     description: "Quick, stylish designs perfect for festive occasions and celebrations.",
//     price: "₹1,500 onwards",
//     features: [
//       "One hand design (front only)",
//       "Contemporary patterns",
//       "1 hour application",
//       "Choice of trendy designs",
//       "Ideal for parties and events"
//     ],
//     image: "https://images.pexels.com/photos/7280300/pexels-photo-7280300.jpeg"
//   }
// ];

// export default function ServicesSection() {
//   const [hoveredService, setHoveredService] = useState<number | null>(null);
  
//   return (
//     <section id="services" className="section-padding bg-muted mehendi-pattern">
//       <div className="container">
//         <div className="text-center mb-12">
//           <h2 className="section-title centered mx-auto">Our Services</h2>
//           <p className="max-w-2xl mx-auto text-muted-foreground">
//             Choose from our range of customizable mehendi services, each tailored 
//             to your specific occasion and style preferences.
//           </p>
//         </div>
        
//         <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
//           {services.map((service) => (
//             <div 
//               key={service.id}
//               className={`bg-white rounded-sm shadow-md overflow-hidden transition-transform duration-300 animate-on-scroll ${
//                 service.popular ? 'border-t-4 border-gold relative' : ''
//               } ${
//                 hoveredService === service.id ? 'translate-y-[-8px]' : ''
//               }`}
//               onMouseEnter={() => setHoveredService(service.id)}
//               onMouseLeave={() => setHoveredService(null)}
//             >
//               {service.popular && (
//                 <div className="absolute top-0 right-0 bg-gold text-white text-xs py-1 px-3 z-10">
//                   Popular
//                 </div>
//               )}
              
//               <div className="relative h-48 overflow-hidden">
//                 <Image
//                   src={service.image}
//                   alt={service.title}
//                   fill
//                   className="object-cover"
//                 />
//               </div>
              
//               <div className="p-6">
//                 <h3 className="font-cormorant text-2xl mb-2">{service.title}</h3>
//                 <p className="font-cormorant text-2xl text-gold font-medium mb-4">{service.price}</p>
//                 <p className="text-sm text-gray-600 mb-6">{service.description}</p>
                
//                 <ul className="space-y-3 mb-6">
//                   {service.features.map((feature, index) => (
//                     <li key={index} className="flex items-start">
//                       <span className="mr-2 text-gold mt-1">
//                         <Check size={16} />
//                       </span>
//                       <span className="text-sm">{feature}</span>
//                     </li>
//                   ))}
//                 </ul>
                
//                 <a 
//                   href="#contact" 
//                   className={`w-full btn ${
//                     service.popular ? 'btn-primary bg-gold border-gold hover:bg-gold/90' : 'btn-outline'
//                   }`}
//                 >
//                   Book Now
//                 </a>
//               </div>
//             </div>
//           ))}
//         </div>
//       </div>
//     </section>
//   );
// }

"use client";

import { useState } from 'react';
import Image from 'next/image';
import { Check } from 'lucide-react';

interface Service {
  id: number;
  title: string;
  description: string;
  price: string;
  features: string[];
  popular?: boolean;
  image: string;
}

const services: Service[] = [
  {
    id: 1,
    title: "Bridal Mehendi",
    description: "Complete bridal mehendi package for hands and feet with intricate traditional designs.",
    price: "₹8,000 onwards",
    features: [
      "Full hands up to elbows",
      "Full feet up to ankles",
      "Personalized design consultation",
      "Complimentary touch-up",
      "Wedding motifs and symbols"
    ],
    popular: true,
    image: "https://images.pexels.com/photos/5088188/pexels-photo-5088188.jpeg"
  },
  {
    id: 2,
    title: "Arabic Style",
    description: "Elegant Arabic-inspired designs featuring floral patterns and negative space.",
    price: "₹3,500 onwards",
    features: [
      "Both hands up to wrist",
      "Bold, spaced-out patterns",
      "Floral and vine designs",
      "Quick-drying formula",
      "2-3 hour application"
    ],
    image: "https://images.pexels.com/photos/7139560/pexels-photo-7139560.jpeg"
  },
  {
    id: 3,
    title: "Party Mehendi",
    description: "Quick, stylish designs perfect for festive occasions and celebrations.",
    price: "₹1,500 onwards",
    features: [
      "One hand design (front only)",
      "Contemporary patterns",
      "1 hour application",
      "Choice of trendy designs",
      "Ideal for parties and events"
    ],
    image: "https://images.pexels.com/photos/7280300/pexels-photo-7280300.jpeg"
  }
];

export default function ServicesSection() {
  const [hoveredService, setHoveredService] = useState<number | null>(null);

  return (
    <section id="services" className="section-padding bg-muted mehendi-pattern">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="section-title centered mx-auto">Our Services</h2>
          <p className="max-w-2xl mx-auto text-muted-foreground">
            Choose from our range of customizable mehendi services, each tailored
            to your specific occasion and style preferences.
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {services.map((service) => (
            <div
              key={service.id}
              className={`bg-white rounded-sm shadow-md overflow-hidden transition-all duration-300 animate-on-scroll hover:shadow-lg ${
                service.popular ? 'border-t-4 border-gold relative' : ''
              }`}
              onMouseEnter={() => setHoveredService(service.id)}
              onMouseLeave={() => setHoveredService(null)}
            >
              {service.popular && (
                <div className="absolute top-0 right-0 bg-gold text-white text-xs py-1 px-3 z-10">
                  Popular
                </div>
              )}

              <div className="relative h-48 overflow-hidden">
                <Image
                  src={service.image}
                  alt={service.title}
                  fill
                  className="object-cover"
                />
              </div>

              <div className="p-6">
                <h3 className="font-cormorant text-2xl mb-2">{service.title}</h3>
                <p className="font-cormorant text-2xl text-gold font-medium mb-4">{service.price}</p>
                <p className="text-sm text-gray-600 mb-6">{service.description}</p>

                <ul className="space-y-3 mb-6">
                  {service.features.map((feature, index) => (
                    <li key={index} className="flex items-start">
                      <span className="mr-2 text-gold mt-1">
                        <Check size={16} />
                      </span>
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>

                <a
                  href="#contact"
                  className={`w-full btn ${
                    service.popular ? 'btn-primary bg-gold border-gold hover:bg-gold/90' : 'btn-outline'
                  }`}
                >
                  Book Now
                </a>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}