import Image from 'next/image';

export default function AboutSection() {
  return (
    <section id="about" className="section-padding bg-cream">
      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
          {/* Image */}
          <div className="animate-on-scroll">
            <div className="relative overflow-hidden">
              <Image
                src="https://images.pexels.com/photos/10626376/pexels-photo-10626376.jpeg"
                alt="Artist applying mehendi"
                width={600}
                height={750}
                className="w-full object-cover h-[500px] md:h-[600px]"
              />
              <div className="absolute inset-0 border-[10px] border-white/20 pointer-events-none"></div>
            </div>
          </div>
          
          {/* Content */}
          <div className="animate-on-scroll delay-100">
            <h2 className="section-title">About The Artist</h2>
            <p className="mb-6 text-lg font-cormorant text-primary/90">
              Welcome to Manya Mehendi Creations, where tradition meets artistry.
            </p>
            <p className="mb-4">
              With over 8 years of experience, I specialize in creating bespoke mehendi designs that 
              tell your unique story. My journey began with a deep appreciation for the cultural significance 
              of mehendi and has evolved into a passion for contemporary artistry.
            </p>
            <p className="mb-6">
              Every design is personally crafted with meticulous attention to detail, 
              ensuring that your special moments are adorned with elegance and meaning. 
              From traditional bridal patterns to modern interpretations, my work combines 
              timeless techniques with innovative designs.
            </p>
            
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-8">
              <div className="text-center">
                <p className="font-cormorant text-4xl font-medium text-gold">8+</p>
                <p className="text-sm uppercase tracking-wider mt-1">Years Experience</p>
              </div>
              <div className="text-center">
                <p className="font-cormorant text-4xl font-medium text-gold">500+</p>
                <p className="text-sm uppercase tracking-wider mt-1">Happy Clients</p>
              </div>
              <div className="text-center">
                <p className="font-cormorant text-4xl font-medium text-gold">50+</p>
                <p className="text-sm uppercase tracking-wider mt-1">Weddings</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}