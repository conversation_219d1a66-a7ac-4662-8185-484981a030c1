"use client";

import { useState } from 'react';
import { CalendarIcon, Clock, MapPin, Phone, Mail, Send } from 'lucide-react';

export default function ContactSection() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    date: '',
    service: '',
    message: '',
  });
  
  const [formStatus, setFormStatus] = useState<{
    submitted: boolean;
    success: boolean;
    message: string;
  } | null>(null);
  
  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Simulate form submission
    setFormStatus({
      submitted: true,
      success: true,
      message: 'Thank you for your inquiry! We will get back to you shortly.'
    });
    
    // Reset form after submission
    setFormData({
      name: '',
      email: '',
      phone: '',
      date: '',
      service: '',
      message: '',
    });
    
    // Reset form status after 5 seconds
    setTimeout(() => {
      setFormStatus(null);
    }, 5000);
  };
  
  return (
    <section id="contact" className="section-padding bg-white">
      <div className="container">
        <div className="text-center mb-12">
          <h2 className="section-title centered mx-auto">Contact Us</h2>
          <p className="max-w-2xl mx-auto text-muted-foreground">
            Ready to book your mehendi session or have questions? Reach out to us using the form below 
            or through our contact information.
          </p>
        </div>
        
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Contact Information */}
          <div className="animate-on-scroll">
            <div className="bg-muted p-8 h-full">
              <h3 className="font-cormorant text-2xl mb-6">Get in Touch</h3>
              
              <div className="space-y-6">
                <div className="flex items-start space-x-4">
                  <div className="bg-white p-3 rounded-sm shadow-sm">
                    <MapPin size={20} className="text-gold" />
                  </div>
                  <div>
                    <h4 className="font-medium mb-1">Location</h4>
                    <p className="text-muted-foreground text-sm">Serving Delhi, NCR Region</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="bg-white p-3 rounded-sm shadow-sm">
                    <Phone size={20} className="text-gold" />
                  </div>
                  <div>
                    <h4 className="font-medium mb-1">Phone</h4>
                    <p className="text-muted-foreground text-sm">+91 98765 43210</p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="bg-white p-3 rounded-sm shadow-sm">
                    <Mail size={20} className="text-gold" />
                  </div>
                  <div>
                    <h4 className="font-medium mb-1">Email</h4>
                    <p className="text-muted-foreground text-sm"><EMAIL></p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-4">
                  <div className="bg-white p-3 rounded-sm shadow-sm">
                    <Clock size={20} className="text-gold" />
                  </div>
                  <div>
                    <h4 className="font-medium mb-1">Working Hours</h4>
                    <p className="text-muted-foreground text-sm">
                      Monday - Saturday: 10 AM - 8 PM<br />
                      Sunday: By appointment only
                    </p>
                  </div>
                </div>
              </div>
              
              <div className="mt-8 pt-8 border-t border-border">
                <h4 className="font-medium mb-4">Follow Us</h4>
                <div className="flex space-x-4">
                  <a 
                    href="#" 
                    className="bg-white w-10 h-10 rounded-sm flex items-center justify-center shadow-sm hover:bg-gold hover:text-white transition-colors"
                    aria-label="Instagram"
                  >
                    <i className="ri-instagram-line"></i>
                  </a>
                  <a 
                    href="#" 
                    className="bg-white w-10 h-10 rounded-sm flex items-center justify-center shadow-sm hover:bg-gold hover:text-white transition-colors"
                    aria-label="Facebook"
                  >
                    <i className="ri-facebook-line"></i>
                  </a>
                  <a 
                    href="#" 
                    className="bg-white w-10 h-10 rounded-sm flex items-center justify-center shadow-sm hover:bg-gold hover:text-white transition-colors"
                    aria-label="WhatsApp"
                  >
                    <i className="ri-whatsapp-line"></i>
                  </a>
                </div>
              </div>
            </div>
          </div>
          
          {/* Contact Form */}
          <div className="animate-on-scroll delay-100">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 gap-6 sm:grid-cols-2">
                <div>
                  <label htmlFor="name" className="block text-sm font-medium mb-2">
                    Your Name <span className="text-accent">*</span>
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                    required
                    className="w-full border border-input px-4 py-3 bg-transparent focus:outline-none focus:ring-1 focus:ring-gold"
                    placeholder="Enter your name"
                  />
                </div>
                
                <div>
                  <label htmlFor="email" className="block text-sm font-medium mb-2">
                    Email Address <span className="text-accent">*</span>
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                    required
                    className="w-full border border-input px-4 py-3 bg-transparent focus:outline-none focus:ring-1 focus:ring-gold"
                    placeholder="Enter your email"
                  />
                </div>
                
                <div>
                  <label htmlFor="phone" className="block text-sm font-medium mb-2">
                    Phone Number <span className="text-accent">*</span>
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handleChange}
                    required
                    className="w-full border border-input px-4 py-3 bg-transparent focus:outline-none focus:ring-1 focus:ring-gold"
                    placeholder="Enter your phone number"
                  />
                </div>
                
                <div>
                  <label htmlFor="date" className="block text-sm font-medium mb-2">
                    Preferred Date
                  </label>
                  <div className="relative">
                    <input
                      type="date"
                      id="date"
                      name="date"
                      value={formData.date}
                      onChange={handleChange}
                      className="w-full border border-input px-4 py-3 bg-transparent focus:outline-none focus:ring-1 focus:ring-gold"
                    />
                    <CalendarIcon className="absolute right-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground pointer-events-none" />
                  </div>
                </div>
                
                <div className="sm:col-span-2">
                  <label htmlFor="service" className="block text-sm font-medium mb-2">
                    Service Interest <span className="text-accent">*</span>
                  </label>
                  <select
                    id="service"
                    name="service"
                    value={formData.service}
                    onChange={handleChange}
                    required
                    className="w-full border border-input px-4 py-3 bg-transparent focus:outline-none focus:ring-1 focus:ring-gold"
                  >
                    <option value="" disabled>Select a service</option>
                    <option value="Bridal Mehendi">Bridal Mehendi</option>
                    <option value="Arabic Style">Arabic Style</option>
                    <option value="Party Mehendi">Party Mehendi</option>
                    <option value="Other">Other (Specify in message)</option>
                  </select>
                </div>
                
                <div className="sm:col-span-2">
                  <label htmlFor="message" className="block text-sm font-medium mb-2">
                    Your Message
                  </label>
                  <textarea
                    id="message"
                    name="message"
                    value={formData.message}
                    onChange={handleChange}
                    rows={4}
                    className="w-full border border-input px-4 py-3 bg-transparent focus:outline-none focus:ring-1 focus:ring-gold resize-none"
                    placeholder="Tell us more about your event and requirements..."
                  ></textarea>
                </div>
              </div>
              
              <button
                type="submit"
                className="btn btn-primary bg-gold border-gold hover:bg-gold/90 w-full sm:w-auto flex items-center justify-center gap-2"
              >
                <span>Send Message</span>
                <Send size={16} />
              </button>
              
              {formStatus && (
                <div className={`mt-4 p-4 rounded-sm ${formStatus.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
                  {formStatus.message}
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </section>
  );
}