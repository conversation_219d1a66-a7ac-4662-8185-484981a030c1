import Link from 'next/link';
import { Facebook, Instagram, Mail, MapPin, Phone } from 'lucide-react';

export default function Footer() {
  return (
    <footer className="bg-primary text-white py-12">
      <div className="container">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <h3 className="font-cormorant text-2xl font-medium">Manya Mehendi Creations</h3>
            <p className="text-sm text-gray-300 max-w-xs">
              Transforming special moments with exquisite henna artistry since 2015.
            </p>
            <div className="flex space-x-4 pt-2">
              <Link href="#" className="hover:text-gold transition-colors">
                <Instagram size={20} />
              </Link>
              <Link href="#" className="hover:text-gold transition-colors">
                <Facebook size={20} />
              </Link>
            </div>
          </div>
          
          {/* Quick Links */}
          <div className="space-y-4">
            <h4 className="font-cormorant text-xl font-medium">Quick Links</h4>
            <ul className="space-y-2">
              <li>
                <Link href="#home" className="text-sm text-gray-300 hover:text-gold transition-colors">
                  Home
                </Link>
              </li>
              <li>
                <Link href="#about" className="text-sm text-gray-300 hover:text-gold transition-colors">
                  About
                </Link>
              </li>
              <li>
                <Link href="#gallery" className="text-sm text-gray-300 hover:text-gold transition-colors">
                  Gallery
                </Link>
              </li>
              <li>
                <Link href="#services" className="text-sm text-gray-300 hover:text-gold transition-colors">
                  Services
                </Link>
              </li>
              <li>
                <Link href="#contact" className="text-sm text-gray-300 hover:text-gold transition-colors">
                  Contact
                </Link>
              </li>
            </ul>
          </div>
          
          {/* Contact */}
          <div className="space-y-4">
            <h4 className="font-cormorant text-xl font-medium">Contact Us</h4>
            <ul className="space-y-3">
              <li className="flex items-start space-x-3">
                <MapPin size={18} className="text-gold mt-1 flex-shrink-0" />
                <span className="text-sm text-gray-300">Delhi, NCR Region</span>
              </li>
              <li className="flex items-start space-x-3">
                <Phone size={18} className="text-gold mt-1 flex-shrink-0" />
                <span className="text-sm text-gray-300">+91 98765 43210</span>
              </li>
              <li className="flex items-start space-x-3">
                <Mail size={18} className="text-gold mt-1 flex-shrink-0" />
                <span className="text-sm text-gray-300"><EMAIL></span>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="mt-12 pt-8 border-t border-gray-800 text-center text-xs text-gray-500">
          <p>© {new Date().getFullYear()} Manya Mehendi Creations. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
}