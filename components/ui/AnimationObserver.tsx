"use client";

import { useEffect, useRef, ReactNode } from 'react';

interface AnimationObserverProps {
  children: ReactNode;
}

export default function AnimationObserver({ children }: AnimationObserverProps) {
  const observerRef = useRef<IntersectionObserver | null>(null);
  
  useEffect(() => {
    // Setup intersection observer
    observerRef.current = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('is-visible');
          
          // Unobserve after animation is triggered
          observerRef.current?.unobserve(entry.target);
        }
      });
    }, {
      root: null,
      rootMargin: '0px',
      threshold: 0.1
    });
    
    // Observe all elements with animate-on-scroll class
    const animatedElements = document.querySelectorAll('.animate-on-scroll');
    animatedElements.forEach(el => {
      observerRef.current?.observe(el);
    });
    
    // Cleanup
    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, []);
  
  return <>{children}</>;
}