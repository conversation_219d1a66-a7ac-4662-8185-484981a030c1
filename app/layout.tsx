import './globals.css';
import type { Metada<PERSON> } from 'next';
import { <PERSON><PERSON><PERSON><PERSON>_<PERSON>, Montserrat } from 'next/font/google';
import { ThemeProvider } from '@/components/theme-provider';

const cormorant = Cormorant_Garamond({ 
  subsets: ['latin'],
  weight: ['300', '400', '500', '600', '700'],
  variable: '--font-cormorant',
});

const montserrat = Montserrat({ 
  subsets: ['latin'],
  weight: ['300', '400', '500', '600'],
  variable: '--font-montserrat',
});

export const metadata: Metadata = {
  title: 'Manya Mehendi Creations | Elegant Henna Designs',
  description: 'Exquisite mehendi designs for weddings, special occasions, and celebrations by Manya Mehendi Creations.',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${cormorant.variable} ${montserrat.variable} font-sans`}>
        <ThemeProvider
          attribute="class"
          defaultTheme="light"
          enableSystem
          disableTransitionOnChange
        >
          {children}
        </ThemeProvider>
      </body>
    </html>
  );
}